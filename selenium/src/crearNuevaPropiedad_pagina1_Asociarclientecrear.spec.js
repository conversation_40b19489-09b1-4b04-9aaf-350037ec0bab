// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Crear Nueva Propiedad - Asociar cliente crear', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Crear Nueva Propiedad - Asociar cliente crear', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".button--primary-solid")).click()
    await driver.findElement(By.css("#button-associate-client > .mat-ripple")).click()
    await driver.findElement(By.css("#mat-tab-label-0-1 > .mat-tab-label-content")).click()
    await driver.findElement(By.css(".field-column:nth-child(1) .grey-border")).click()
    await driver.findElement(By.xpath("//input[@type=\'undefined\']")).sendKeys("Priscila")
    await driver.findElement(By.css(".item-align-right .grey-border")).click()
    {
      const element = await driver.findElement(By.css(".field-container:nth-child(3) .selected"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".item-align-right .grey-border")).sendKeys("Misiak")
    {
      const element = await driver.findElement(By.CSS_SELECTOR, "body")
      await driver.actions({ bridge: true }).moveToElement(element, 0, 0).perform()
    }
    await driver.findElement(By.xpath("//button[contains(.,\'Femenino\')]")).click()
    await driver.executeScript("window.scrollTo(0,0)")
    await driver.findElement(By.xpath("//button[contains(.,\'Activo\')]")).click()
    await driver.findElement(By.xpath("//span[contains(.,\'Seleccione una opción\')]")).click()
    await driver.findElement(By.xpath("//span[contains(.,\'Conocido/Familiar\')]")).click()
    await driver.findElement(By.xpath("//mat-select[@id=\'mat-select-28\']/div/div[2]")).click()
    await driver.findElement(By.xpath("//mat-option[@id=\'mat-option-520\']/span")).click()
    await driver.findElement(By.xpath("//button[contains(.,\'Crear cliente\')]")).click()
    await driver.executeScript("window.scrollTo(0,0)")
    await driver.findElement(By.css("#button-associate-client > .button--primary-solid")).click()
    {
      const element = await driver.findElement(By.css("#button-back > .mat-ripple"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    {
      const element = await driver.findElement(By.CSS_SELECTOR, "body")
      await driver.actions({ bridge: true }).moveToElement(element, 0, 0).perform()
    }
  })
})
