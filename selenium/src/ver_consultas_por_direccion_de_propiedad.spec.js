// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('ver_consultas_por_direccion_de_propiedad', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('ver_consultas_por_direccion_de_propiedad', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".menu__section:nth-child(3) .menu__icon")).click()
    await driver.findElement(By.css(".date-item:nth-child(1) > .date-picker")).click()
    await driver.findElement(By.css(".date-item:nth-child(1) > .date-picker")).sendKeys("2025-03-02")
    await driver.findElement(By.css(".date-item:nth-child(2) > .date-picker")).click()
    await driver.findElement(By.css(".date-item:nth-child(2) > .date-picker")).sendKeys("2025-03-05")
  })
})
