// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Buscar propiedad - Rechazadas', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Buscar propiedad - Rechazadas', async function() {
    await driver.get("https://redremax.com/?returnUrl=%2Fpropiedades")
    await driver.findElement(By.id("mat-select-value-1")).click()
    await driver.findElement(By.css("#mat-option-3 > .mat-option-text")).click()
    await driver.findElement(By.css(".grey-border")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("torre zafiro")
  })
})
