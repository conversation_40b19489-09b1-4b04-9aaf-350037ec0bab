/**
 * Plantilla para scripts de Selenium
 *
 * Este archivo sirve como ejemplo para crear nuevos scripts de Selenium.
 * El manejo de errores y el registro de logs se realiza de forma centralizada en index.js.
 *
 * Notas importantes:
 * - No es necesario incluir try/catch, ya que los errores se manejan en index.js
 * - Usa global.log() para registrar mensajes en el archivo de log
 * - En caso de error, el HTML de la página se guarda automáticamente
 */
module.exports = async function({ driver, By, until }, parametros) {
    // URL a la que se va a navegar (puedes usar parámetros pasados desde n8n)
    const url = parametros?.url || "https://redremax.com/";

    global.log(`Navegando a: ${url}`);
    await driver.get(url);

    // Aquí va la lógica específica del script
    global.log('Ejecutando acciones específicas del script');

    // Ejemplo: buscar un elemento y hacer clic en él
    // const elemento = await driver.wait(until.elementLocated(By.css(".selector-css")), 5000);
    // await elemento.click();

    // Ejemplo: obtener el título de la página
    const title = await driver.getTitle();
    global.log(`Título de la página: ${title}`);

    // Devolver resultados
    return [{ title: title }];
};
