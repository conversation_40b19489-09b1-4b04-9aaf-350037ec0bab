module.exports = async function({ driver, By, until }, parametros) {
    await driver.get("https://redremax.com/propiedades")
    {
      const element = await driver.findElement(By.css(".button--primary-solid"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".button--primary-solid")).click()
    {
      const element = await driver.findElement(By.xpath("//button[contains(.,\'Alquiler\')]"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.xpath("//button[contains(.,\'Alquiler\')]")).click()
};