/**
 * Script para iniciar sesión en la plataforma
 */
module.exports = async function({ driver, By, until }, parametros) {
    // Usamos la función global log definida en index.js
    global.log('Iniciando sesión en https://redremax.com/');
    await driver.get("https://redremax.com/");

    global.log('Esperando a que los campos de entrada estén presentes');

    // Esperar a que los campos de entrada estén presentes y visibles
    const usernameInput = await driver.wait(until.elementLocated(By.xpath("//input[@type='undefined']")), 5000);
    const passwordInput = await driver.wait(until.elementLocated(By.xpath("//input[@type='password']")), 5000);

    global.log('Ingresando credenciales');
    await usernameInput.sendKeys("<EMAIL>");
    await passwordInput.sendKeys("Alvaro2023");

    global.log('Haciendo clic en el botón de login');
    // Buscar y hacer clic en el botón de login
    const loginButton = await driver.wait(until.elementLocated(By.xpath("(//button[@type='button'])[2]")), 5000);
    await loginButton.click();

    global.log('Inicio de sesión completado con éxito');
};
