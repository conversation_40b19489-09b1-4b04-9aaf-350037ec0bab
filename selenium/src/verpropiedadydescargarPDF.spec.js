// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Ver propiedad y descargar PDF', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Ver propiedad y descargar PDF', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".grey-border")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("gatica 2843")
    await driver.findElement(By.css(".grey-border")).sendKeys(Key.ENTER)
    await driver.findElement(By.css(".picture .mat-tooltip-trigger")).click()
    await driver.findElement(By.css("app-prop-item")).click()
    {
      const element = await driver.findElement(By.css(".picture .mat-tooltip-trigger"))
      await driver.actions({ bridge: true}).doubleClick(element).perform()
    }
    {
      const element = await driver.findElement(By.css(".icon-container:nth-child(2) .mat-ripple"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".icon-container:nth-child(2) .mat-ripple")).click()
    {
      const element = await driver.findElement(By.CSS_SELECTOR, "body")
      await driver.actions({ bridge: true }).moveToElement(element, 0, 0).perform()
    }
    await driver.findElement(By.xpath("//button[contains(.,\'Extendida\')]")).click()
    {
      const element = await driver.findElement(By.css(".options__btn-export > .mat-ripple"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".options__btn-export > .mat-ripple")).click()
  })
})
