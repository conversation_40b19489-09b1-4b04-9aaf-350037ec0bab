// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Buscar propiedad - Pendientes', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Buscar propiedad - Pendientes', async function() {
    await driver.get("https://redremax.com/?returnUrl=%2Fpropiedades")
    await driver.findElement(By.css(".mat-select-arrow-wrapper")).click()
    await driver.findElement(By.css("#mat-option-2 > .mat-option-text")).click()
  })
})
