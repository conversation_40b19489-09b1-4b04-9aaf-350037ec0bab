// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Crear Nueva Propiedad - Tipo de propiedad y subcategoría', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Crear Nueva Propiedad - Tipo de propiedad y subcategoría', async function() {
    await driver.get("https://redremax.com/propiedades")
    {
      const element = await driver.findElement(By.css(".button--primary-solid"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".button--primary-solid")).click()
    {
      const element = await driver.findElement(By.css(".ng-tns-c93-20 > .mat-form-field-infix"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".ng-tns-c93-20 > .mat-form-field-infix")).click()
    await driver.findElement(By.xpath("//mat-option[contains(.,\'Casa\')]")).click()
    await driver.findElement(By.xpath("//div[@id=\'subtype-property\']/app-select-list-v2-input/mat-form-field/div/div/div")).click()
    await driver.findElement(By.id("mat-option-26")).click()
  })
})
