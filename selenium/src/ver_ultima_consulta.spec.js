/**
 * Script para ver las últimas 3 consultas
 */
module.exports = async function({ driver, By, until }, parametros) {
    try {
        global.log('Iniciando script ver_ultima_consulta');

        // Navegar a la página de consultas
        global.log('Navegando a https://redremax.com/consultas');
        await driver.get("https://redremax.com/consultas");

        // Esperar a que la página se cargue completamente (máximo 10 segundos)
        global.log('Esperando a que la página se cargue completamente');
        await driver.wait(until.titleContains("RE/MAX"), 10000);

        // Verificar la URL actual
        const currentUrl = await driver.getCurrentUrl();
        global.log(`URL actual: ${currentUrl}`);

        // Buscar un elemento que sea app-consultas-item
        global.log('Buscando un elemento app-consultas-item');
        const consultasItem = await driver.wait(until.elementLocated(By.xpath("//app-consultas-item")), 5000);

        // Generar una respuesta json con los datos de las primeras 3 consultas
        global.log('Generando respuesta JSON con los datos de las primeras 3 consultas');
        const consultas = await driver.findElements(By.xpath("//app-consultas-item"));
        const resultados = [];
        for (let i = 0; i < 3 && i < consultas.length; i++) {
            const consulta = consultas[i];
            // Por ahora solo saco el dato del elemento p de class='text'
            const texto = await consulta.findElement(By.xpath(".//p[@class='text']")).getText();
            resultados.push({ texto });
        }

        return resultados;

    } catch (error) {
        // Manejar cualquier error no capturado en los bloques try/catch internos
        await global.handleError(error, { context: 'Error general en ver_ultima_consulta' });
        return [{ error: error.message }];
    }
};
