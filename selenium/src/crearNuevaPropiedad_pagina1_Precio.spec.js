// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Crear Nueva Propiedad - Precio', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Crear Nueva Propiedad - Precio', async function() {
    await driver.get("https://redremax.com/propiedades")
    {
      const element = await driver.findElement(By.css(".button--primary-solid"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".button--primary-solid")).click()
    await driver.findElement(By.css(".ng-star-inserted > .price-content .grey-border")).click()
    await driver.findElement(By.css(".price-content:nth-child(2) .ng-touched")).sendKeys("280.000")
  })
})
