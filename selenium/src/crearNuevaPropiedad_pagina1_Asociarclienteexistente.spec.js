// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Crear Nueva Propiedad - Asociar cliente existente', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Crear Nueva Propiedad - Asociar cliente existente', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".button--primary-solid")).click()
    await driver.findElement(By.css("#button-associate-client > .mat-ripple")).click()
    await driver.findElement(By.css("#search-input > input")).sendKeys("<PERSON>")
    await driver.findElement(By.css("#mat-radio-11 .mat-radio-outer-circle")).click()
    {
      const element = await driver.findElement(By.css("#button-associate-client > .button--primary-solid"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css("#button-associate-client > .button--primary-solid")).click()
  })
})
