/**
 * Script para sacar cartel de novedades cuando se inicia sesión. Aparece sólo a veces.
 */
module.exports = async function({ driver, By, until }, parametros) {
    global.log('Iniciando script carteldenovedades');

    try {
        // Verificar si el botón de cierre del cartel está presente
        global.log('Verificando si existe un cartel con botón de cierre (id=close)');

        // Esperar a que el elemento de cierre esté presente (con un timeout corto porque puede no aparecer)
        const closeElement = await driver.wait(until.elementLocated(By.id("close")), 5000)
            .catch(() => null); // Si no se encuentra, devuelve null

        if (closeElement) {
            global.log('Botón de cierre encontrado. Cerrando el cartel...');

            // Buscar el botón de cierre (puede ser el elemento en sí o un hijo)
            let closeButton;
            try {
                // Intentar encontrar el elemento con la clase mat-ripple dentro del elemento close
                closeButton = await closeElement.findElement(By.css(".mat-ripple"));
                global.log('Encontrado botón de cierre con clase mat-ripple');
            } catch (e) {
                // Si no se encuentra, usar el elemento close directamente
                closeButton = closeElement;
                global.log('Usando el elemento close directamente como botón');
            }

            // // Mover el cursor al botón de cierre
            // await driver.actions({ bridge: true }).moveToElement(closeButton).perform();
            // global.log('Cursor posicionado sobre el botón de cierre');

            // // Mover el cursor al cuerpo para asegurarse de que no hay hover effects
            // const bodyElement = await driver.findElement(By.css("body"));
            // await driver.actions({ bridge: true }).moveToElement(bodyElement, 0, 0).perform();

            // Hacer clic en el botón de cierre
            await closeButton.click();
            global.log('Cartel cerrado correctamente');
        } else {
            global.log('No se encontró ningún cartel con botón de cierre. Continuando con la ejecución.');
        }

        return [{ resultado: "Cartel de novedades procesado correctamente" }];
    } catch (error) {
        // Usar la función global de manejo de errores
        await global.handleError(error, { context: 'Procesando cartel de novedades' });

        // No lanzamos el error para que el script pueda continuar
        return [{ resultado: "Error al procesar el cartel de novedades", error: error.message }];
    }
};
