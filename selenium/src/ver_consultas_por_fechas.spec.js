// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('ver_consultas_por_fechas', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('ver_consultas_por_fechas', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.xpath("//qr-input[@id=\'search-properties\']/div/input")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("<PERSON><PERSON> Moreno")
    await driver.findElement(By.css(".grey-border")).sendKeys(Key.ENTER)
    {
      const elements = await driver.findElements(By.css(".item-prop-column-picture"))
      assert(elements.length)
    }
    await driver.findElement(By.css("#icon-panel-messages > img")).click()
  })
})
