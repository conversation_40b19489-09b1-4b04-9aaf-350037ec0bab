// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Crear Nueva Propiedad - Expensas', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Crear Nueva Propiedad - Expensas', async function() {
    await driver.get("https://redremax.com/propiedades")
    {
      const element = await driver.findElement(By.css(".button--primary-solid"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".button--primary-solid")).click()
    await driver.findElement(By.xpath("(//input[@type=\'undefined\'])[2]")).click()
    await driver.findElement(By.xpath("(//input[@type=\'undefined\'])[2]")).sendKeys("30.000")
  })
})
