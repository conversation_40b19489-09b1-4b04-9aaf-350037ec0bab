// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Buscar propiedad en Activas', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Buscar propiedad en Activas', async function() {
    await driver.get("https://redremax.com/?returnUrl=%2Fpropiedades")
    await driver.findElement(By.css(".grey-border")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("Gatica 2843")
    await driver.findElement(By.css(".grey-border")).sendKeys(Key.ENTER)
    {
      const element = await driver.findElement(By.css("#filters-button > .mat-ripple"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    {
      const element = await driver.findElement(By.CSS_SELECTOR, "body")
      await driver.actions({ bridge: true }).moveToElement(element, 0, 0).perform()
    }
  })
})
