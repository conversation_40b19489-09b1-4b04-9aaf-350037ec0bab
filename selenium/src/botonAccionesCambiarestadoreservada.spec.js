// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Botón Acciones - Cambiar estado (reservada)', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('Botón Acciones - Cambiar estado (reservada)', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".grey-border")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("Lorenzo Crease")
    await driver.findElement(By.css(".grey-border")).sendKeys(Key.ENTER)
    {
      const element = await driver.findElement(By.css(".mat-menu-trigger > .mat-ripple"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".mat-menu-trigger > .mat-ripple")).click()
    await driver.findElement(By.xpath("//button[contains(.,\'Cambiar estado\')]")).click()
    await driver.findElement(By.css(".ng-tns-c93-17 > .mat-form-field-infix")).click()
    await driver.findElement(By.xpath("//mat-option[contains(.,\'Reservada\')]")).click()
    await driver.findElement(By.css(".botbar__btn-request > .mat-ripple")).click()
  })
})
