// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('ver_consultas_por_MLS_ID', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('ver_consultas_por_MLS_ID', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".menu__section:nth-child(3) .menu__icon")).click()
    await driver.findElement(By.xpath("//div[@id=\'search-container\']/div/qr-input/div/input")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("420371079-336")
  })
})
