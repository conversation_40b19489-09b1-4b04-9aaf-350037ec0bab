// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('Botón Acciones - Link web RE/MAX', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  async function waitForWindow(timeout = 2) {
    await driver.sleep(timeout)
    const handlesThen = vars["windowHandles"]
    const handlesNow = await driver.getAllWindowHandles()
    if (handlesNow.length > handlesThen.length) {
      return handlesNow.find(handle => (!handlesThen.includes(handle)))
    }
    throw new Error("New window did not appear before timeout")
  }
  it('Botón Acciones - Link web RE/MAX', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".grey-border")).click()
    await driver.findElement(By.css(".grey-border")).sendKeys("gatica 2843")
    await driver.findElement(By.css(".grey-border")).sendKeys(Key.ENTER)
    {
      const element = await driver.findElement(By.css(".mat-menu-trigger > .mat-ripple"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.css(".mat-menu-trigger > .mat-ripple")).click()
    await driver.findElement(By.css(".mat-menu-trigger > .mat-ripple")).click()
    {
      const element = await driver.findElement(By.CSS_SELECTOR, "body")
      await driver.actions({ bridge: true }).moveToElement(element, 0, 0).perform()
    }
    vars["windowHandles"] = await driver.getAllWindowHandles()
    await driver.findElement(By.xpath("//button[contains(.,\'Ver en RE/MAX\')]")).click()
    vars["win5872"] = await waitForWindow(2000)
    await driver.get("https://redremax.com/?returnUrl=%2Fpropiedades")
  })
})
