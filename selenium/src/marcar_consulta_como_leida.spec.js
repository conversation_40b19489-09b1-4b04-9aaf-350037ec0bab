// Generated by Selenium IDE
const { Builder, By, Key, until } = require('selenium-webdriver')
const assert = require('assert')

describe('marcar_consulta_como_leida', function() {
  this.timeout(30000)
  let driver
  let vars
  beforeEach(async function() {
    driver = await new Builder().forBrowser('firefox').build()
    vars = {}
  })
  afterEach(async function() {
    await driver.quit();
  })
  it('marcar_consulta_como_leida', async function() {
    await driver.get("https://redremax.com/propiedades")
    await driver.findElement(By.css(".menu__section:nth-child(3) .menu__icon")).click()
    await driver.findElement(By.xpath("//button[contains(.,\'Menú\')]")).click()
    {
      const element = await driver.findElement(By.css(".mat-menu-item:nth-child(2)"))
      await driver.actions({ bridge: true }).moveToElement(element).perform()
    }
    await driver.findElement(By.xpath("//button[contains(.,\'Leído\')]")).click()
  })
})
