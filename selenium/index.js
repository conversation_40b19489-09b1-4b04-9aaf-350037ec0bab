const fs = require('fs');
const path = require('path');
const { Builder, By, until } = require('selenium-webdriver');
const firefox = require('selenium-webdriver/firefox');

// Función para asegurar que una carpeta existe
function asegurarCarpeta(carpeta) {
    if (!fs.existsSync(carpeta)) {
        fs.mkdirSync(carpeta, { recursive: true });
    }
}

// Función para registrar logs
function registrarLog(logFilePath, mensaje) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${mensaje}\n`;

    try {
        // Añadir al archivo de log (o crearlo si no existe)
        fs.appendFileSync(logFilePath, logEntry);
    } catch (err) {
        console.error(`Error al escribir en el archivo de log ${logFilePath}:`, err);
    }
}

// Variables globales
let globalLogFunction = null;
let globalDriver = null; // Driver global que será reutilizado entre scripts
let globalRequestId = null; // RequestId actual
let globalErrorCount = 0; // Contador de errores para numerar capturas

// Función para inicializar el driver de Selenium (solo se llama una vez)
async function inicializarDriver() {
    if (globalDriver) {
        return globalDriver; // Devolver el driver existente si ya está inicializado
    }

    const options = new firefox.Options();
    options.addArguments('-headless'); // Configurar Firefox para ejecutarse en modo sin cabeza

    globalDriver = await new Builder()
        .forBrowser('firefox')
        .setFirefoxOptions(options)
        .build();

    return globalDriver;
}

// Función para cerrar el driver de Selenium (solo se llama al final)
async function cerrarDriver() {
    if (globalDriver) {
        try {
            await globalDriver.quit();
            globalDriver = null;
        } catch (error) {
            console.error('Error al cerrar el driver:', error);
        }
    }
}

// Función global para manejar errores en los scripts
async function handleError(error, errorInfo = {}) {
    if (!globalDriver || !globalLogFunction) {
        console.error('Error: handleError llamado sin driver o función de log inicializados');
        return;
    }

    // Incrementar el contador de errores
    globalErrorCount++;

    // Registrar el error en el log
    globalLogFunction(`ERROR (${globalErrorCount}): ${error.message}`);
    if (errorInfo.context) {
        globalLogFunction(`Contexto del error: ${errorInfo.context}`);
    }
    globalLogFunction(`Stack: ${error.stack}`);

    try {
        // Obtener la URL actual
        const currentUrl = await globalDriver.getCurrentUrl();
        globalLogFunction(`URL en el momento del error: ${currentUrl}`);

        // Tomar una captura de pantalla
        const screenshotPath = path.resolve(`./logs/${globalRequestId}_error_${globalErrorCount}.png`);
        const screenshot = await globalDriver.takeScreenshot();
        fs.writeFileSync(screenshotPath, Buffer.from(screenshot, 'base64'));
        globalLogFunction(`Captura de pantalla guardada en: ${screenshotPath}`);

        // Guardar el HTML de la página
        const htmlErrorPath = path.resolve(`./logs/${globalRequestId}_error_${globalErrorCount}.html`);
        const pageSource = await globalDriver.getPageSource();
        fs.writeFileSync(htmlErrorPath, pageSource);
        globalLogFunction(`HTML guardado en: ${htmlErrorPath}`);
    } catch (captureError) {
        globalLogFunction(`Error al capturar información adicional: ${captureError.message}`);
    }

    // Devolver un objeto con información sobre el error
    return {
        error: error.message,
        errorCount: globalErrorCount,
        stack: error.stack,
        context: errorInfo.context || 'No context provided'
    };
}

// Función para ejecutar un script de Selenium con manejo centralizado de errores
async function ejecutarScriptSelenium(scriptPath, variables, requestId) {
    const logFilePath = path.resolve(`./logs/${requestId}.log`);
    const htmlErrorPath = path.resolve(`./logs/${requestId}_error.html`);

    // Guardar el requestId globalmente
    globalRequestId = requestId;

    // Asegurar que la carpeta logs existe
    asegurarCarpeta('./logs');

    // Iniciar el log
    registrarLog(logFilePath, `Iniciando ejecución del script: ${scriptPath}`);

    try {
        // Asegurarse de que el driver está inicializado
        const driver = await inicializarDriver();
        registrarLog(logFilePath, `Driver listo para usar`);

        // Creamos un módulo con las dependencias necesarias
        const dependencies = {
            driver,
            By,
            until
        };

        // Definir la función de log global que usarán los scripts
        globalLogFunction = (mensaje) => {
            registrarLog(logFilePath, mensaje);
        };

        // Configurar el contexto global para los scripts
        global.log = globalLogFunction;
        global.handleError = handleError;

        // Cargar y ejecutar el script
        const script = require(scriptPath);

        // Envolvemos la ejecución del script en un try-catch para manejar errores
        registrarLog(logFilePath, `Ejecutando script: ${scriptPath}`);
        const resultados = await script(dependencies, variables);
        registrarLog(logFilePath, `Script ejecutado correctamente`);

        return resultados;
    } catch (error) {
        registrarLog(logFilePath, `ERROR: ${error.message}`);
        registrarLog(logFilePath, `Stack: ${error.stack}`);

        // Capturar el HTML de la página en caso de error
        if (globalDriver) {
            try {
                const currentUrl = await globalDriver.getCurrentUrl();
                registrarLog(logFilePath, `URL en el momento del error: ${currentUrl}`);

                const pageSource = await globalDriver.getPageSource();
                fs.writeFileSync(htmlErrorPath, pageSource);
                registrarLog(logFilePath, `HTML guardado en: ${htmlErrorPath}`);
            } catch (captureError) {
                registrarLog(logFilePath, `Error al capturar el HTML: ${captureError.message}`);
            }
        }

        console.error('Error al ejecutar el script de Selenium:', error);
        throw error;
    } finally {
        // Limpiar la función global de log
        global.log = undefined;
        globalLogFunction = null;
    }
}

async function main() {
    const requestId = process.argv[2];
    if (!requestId) {
        console.log(JSON.stringify({ estado: false, error: 'Se requiere un requestId como argumento.', resultados: [] }));
        return;
    }

    const requestFilePath = `./requests/${requestId}.json`;
    const responseFilePath = `./responses/${requestId}.json`;
    const logFilePath = `./logs/${requestId}.log`;

    // Asegurar que las carpetas existen
    asegurarCarpeta('./requests');
    asegurarCarpeta('./responses');
    asegurarCarpeta('./logs');

    if (!fs.existsSync(requestFilePath)) {
        console.log(JSON.stringify({
            estado: false,
            error: `El archivo de solicitud ${requestId}.json no existe.`,
            resultados: []
        }));
        return;
    }

    let requestData;
    try {
        requestData = JSON.parse(fs.readFileSync(requestFilePath, 'utf8'));
    } catch (error) {
        console.log(JSON.stringify({
            estado: false,
            error: `Error al leer el archivo ${requestId}.json: ${error.message}`,
            resultados: []
        }));
        return;
    }


    const scriptName = requestData.intencion;
    const scriptPath = `./src/${scriptName}.spec.js`;
    const variables = requestData.variables || {};

    if (!fs.existsSync(scriptPath)) {
        console.log(JSON.stringify({ estado: false, error: `El script ${scriptName} no existe.`, resultados: [] }));
        console.log(requestData);
        return;
    }

    try {
        // Primero iniciamos sesión
        await ejecutarScriptSelenium('./src/iniciarsesion.spec.js', variables, requestId);

        // Luego sacamos el cartel de novedades si existe
        await ejecutarScriptSelenium('./src/carteldenovedades.spec.js', variables, requestId);

        // Ejecutamos el script
        const resultados = await ejecutarScriptSelenium(scriptPath, variables, requestId);
        console.log(JSON.stringify({ estado: true, error: null, resultados: resultados }));

        // Guardamos la respuesta en un archivo JSON
        fs.writeFileSync(responseFilePath, JSON.stringify(resultados));

        // Registrar éxito en el log
        registrarLog(logFilePath, `Ejecución completada con éxito. Resultados guardados en: ${responseFilePath}`);
    } catch (error) {
        // Registrar el error en el log (ya se habrá registrado en ejecutarScriptSelenium, pero lo hacemos aquí también)
        registrarLog(logFilePath, `Error en la ejecución principal: ${error.message}`);

        // Guardar una respuesta de error
        const errorResponse = {
            estado: false,
            error: error.message,
            resultados: [],
            logFile: logFilePath,
            htmlErrorFile: `./logs/${requestId}_error.html`
        };

        console.log(JSON.stringify(errorResponse));
        fs.writeFileSync(responseFilePath, JSON.stringify(errorResponse));
    } finally {
        // Cerrar el driver al finalizar todos los scripts
        await cerrarDriver();
        registrarLog(logFilePath, `Driver cerrado correctamente`);
    }
}

main();
