stages:
  - deploy

deploy_to_gce:
  stage: deploy
  script:
    - echo "Conectando al servidor GCE..."
    # Crear el directorio .ssh si no existe
    - mkdir -p ~/.ssh
    # Escribir la clave privada a un archivo
    - echo "$GCE_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    # Dar permisos de solo lectura al archivo de la clave privada
    - chmod 400 ~/.ssh/id_rsa
    # Deshabilitar la verificación estricta del host (puede ser necesario la primera vez)
    - echo "StrictHostKeyChecking no" >> ~/.ssh/config
    - chmod 644 ~/.ssh/config
    # Conectarse al servidor y ejecutar los comandos de despliegue
    - ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa $GCE_USER@$GCE_HOST 'cd agente && git pull && cd selenium && /home/<USER>/.nvm/versions/node/v22.14.0/bin/node /home/<USER>/.nvm/versions/node/v22.14.0/bin/npm install && cd ../app && /home/<USER>/.nvm/versions/node/v22.14.0/bin/node /home/<USER>/.nvm/versions/node/v22.14.0/bin/npm install'
  variables:
    GCE_USER: $GCE_USER
    GCE_HOST: $GCE_HOST
    GCE_PRIVATE_KEY: $GCE_PRIVATE_KEY
  only:
    - main