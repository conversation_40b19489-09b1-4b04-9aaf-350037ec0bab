{"name": "agente-auth-app", "version": "1.0.0", "description": "Sistema de gestión de autorizaciones para n8n workflows", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "authorization", "n8n", "o<PERSON>h"], "author": "Agente.ar", "license": "MIT", "dependencies": {"express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}