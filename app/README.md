# Sistema de Gestión de Autorizaciones - Agente.ar

Sistema Node.js/Express para gestionar autorizaciones y conexiones para workflows de n8n.

## Características Actuales

### ✅ Implementado
- **Registro de números de teléfono**: Interfaz web para capturar números de teléfono como identificadores únicos
- **Sistema de logging**: Almacenamiento de registros en formato JSON con timestamp, IP y datos del teléfono
- **Panel de administración**: Visualización de todos los registros con estadísticas básicas
- **API REST**: Endpoint para registro programático de números
- **Validación**: Validación básica de formato de números de teléfono
- **Interfaz responsive**: UI moderna y responsive con feedback visual

### 🔄 Próximamente
- Autorización con Google Calendar (OAuth2)
- Integración con n8n workflows
- Gestión de access_tokens
- Más proveedores de OAuth

## Estructura del Proyecto

```
app/
├── app.js              # Aplicación principal Express
├── package.json        # Dependencias y scripts
├── logs/              # Directorio de logs
│   └── phone_registrations.log  # Log de registros de teléfonos
└── README.md          # Este archivo
```

## Instalación y Uso

### Prerrequisitos
- Node.js >= 14.0.0
- npm

### Instalación
```bash
cd app
npm install
```

### Ejecución
```bash
# Producción
npm start

# Desarrollo (con nodemon)
npm run dev
```

La aplicación estará disponible en: http://localhost:3000

## Endpoints de la API

### POST /api/register-phone
Registra un nuevo número de teléfono.

**Request:**
```json
{
  "phoneNumber": "+54 9 11 1234-5678"
}
```

**Response (éxito):**
```json
{
  "success": true,
  "message": "Número +54 9 11 1234-5678 registrado exitosamente",
  "timestamp": "2025-07-17T00:30:12.857Z"
}
```

**Response (error):**
```json
{
  "error": "Formato de número de teléfono inválido"
}
```

### GET /
Página principal con formulario de registro.

### GET /admin
Panel de administración con visualización de logs y estadísticas.

### GET /auth
Página de autenticación (placeholder para futuras implementaciones).

## Formato de Logs

Los logs se almacenan en `logs/phone_registrations.log` en formato JSON:

```json
{
  "timestamp": "2025-07-17T00:30:12.857Z",
  "phoneNumber": "+54 9 11 1234-5678",
  "action": "PHONE_REGISTERED",
  "ip": "::ffff:127.0.0.1"
}
```

## Validaciones

- **Número requerido**: El campo phoneNumber no puede estar vacío
- **Formato básico**: Regex `/^[\+]?[0-9\s\-\(\)]{10,}$/` para validar formato
- **Sanitización**: Se eliminan espacios en blanco al inicio y final

## Próximos Pasos Sugeridos

1. **OAuth con Google Calendar**
   - Implementar flujo OAuth2
   - Almacenar y gestionar access_tokens
   - Renovación automática de tokens

2. **Base de datos**
   - Migrar de archivos de log a base de datos (SQLite/PostgreSQL)
   - Relaciones entre usuarios y autorizaciones

3. **Seguridad**
   - Rate limiting
   - Validación más robusta
   - Encriptación de tokens sensibles

4. **Integración n8n**
   - Webhooks para notificar cambios
   - API para consultar estado de autorizaciones

## Tecnologías Utilizadas

- **Backend**: Node.js, Express.js
- **Frontend**: HTML5, CSS3, JavaScript vanilla
- **Logging**: Sistema de archivos JSON
- **Validación**: Regex y validaciones custom
