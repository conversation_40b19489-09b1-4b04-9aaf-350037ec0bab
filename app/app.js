const express = require('express');
const fs = require('fs');
const path = require('path');
const app = express();
const port = 3000;

// Middleware para parsear JSON y URL-encoded
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Crear directorio de logs si no existe
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}



// Ruta principal
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>App Agente.ar - Gestión de Autorizaciones</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
          h1 { color: #333; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .form-group { margin-bottom: 20px; }
          label { display: block; margin-bottom: 5px; font-weight: bold; }
          input[type="tel"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
          button { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
          button:hover { background-color: #0056b3; }
          .info { background-color: #e9ecef; padding: 15px; border-radius: 4px; margin-top: 20px; }
          .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin-top: 20px; display: none; }
          .error { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin-top: 20px; display: none; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Gestión de Autorizaciones</h1>
          <p>Sistema para gestionar conexiones y autorizaciones para n8n workflows</p>

          <form id="phoneForm">
            <div class="form-group">
              <label for="phoneNumber">Número de Teléfono (ID):</label>
              <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="+54 9 11 1234-5678" required>
            </div>
            <button type="submit">Registrar Teléfono</button>
          </form>

          <div id="successMessage" class="success"></div>
          <div id="errorMessage" class="error"></div>

          <div class="info">
            <strong>Estado del Sistema:</strong><br>
            Puerto: ${port}<br>
            Timestamp: ${new Date().toLocaleString()}<br>
            <a href="/admin">Panel de Admin</a> | <a href="/auth">Autenticación</a>
          </div>
        </div>

        <script>
          document.getElementById('phoneForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const phoneNumber = document.getElementById('phoneNumber').value;
            const successDiv = document.getElementById('successMessage');
            const errorDiv = document.getElementById('errorMessage');

            // Limpiar mensajes anteriores
            successDiv.style.display = 'none';
            errorDiv.style.display = 'none';

            try {
              const response = await fetch('/api/register-phone', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ phoneNumber })
              });

              const result = await response.json();

              if (response.ok) {
                successDiv.textContent = result.message;
                successDiv.style.display = 'block';
                document.getElementById('phoneNumber').value = '';
              } else {
                errorDiv.textContent = result.error || 'Error al registrar el teléfono';
                errorDiv.style.display = 'block';
              }
            } catch (error) {
              errorDiv.textContent = 'Error de conexión: ' + error.message;
              errorDiv.style.display = 'block';
            }
          });
        </script>
      </body>
    </html>
  `);
});

// API para registrar número de teléfono
app.post('/api/register-phone', (req, res) => {
  try {
    const { phoneNumber } = req.body;

    // Validación básica
    if (!phoneNumber || phoneNumber.trim() === '') {
      return res.status(400).json({ error: 'Número de teléfono es requerido' });
    }

    // Validación de formato básico (puedes mejorar esto)
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
    if (!phoneRegex.test(phoneNumber.trim())) {
      return res.status(400).json({ error: 'Formato de número de teléfono inválido' });
    }

    // Obtener IP del cliente
    const clientIP = req.headers['x-forwarded-for'] || req.socket.remoteAddress || req.ip || 'unknown';

    // Escribir log con IP
    const logEntry = {
      timestamp: new Date().toISOString(),
      phoneNumber: phoneNumber.trim(),
      action: 'PHONE_REGISTERED',
      ip: clientIP
    };

    const logLine = JSON.stringify(logEntry) + '\n';
    const logFile = path.join(logsDir, 'phone_registrations.log');

    fs.appendFileSync(logFile, logLine);
    console.log(`[${logEntry.timestamp}] ${logEntry.action}: ${logEntry.phoneNumber} from ${clientIP}`);

    res.json({
      success: true,
      message: `Número ${phoneNumber.trim()} registrado exitosamente`,
      timestamp: logEntry.timestamp
    });

  } catch (error) {
    console.error('Error al registrar teléfono:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
});

// Ruta de prueba para auth (futura)
app.get('/auth', (req, res) => {
  res.send('<h1>Sección de Autenticación</h1><p>Próximamente: OAuth con Google Calendar</p>');
});

// Ruta para admin con visualización de logs
app.get('/admin', (req, res) => {
  try {
    const logFile = path.join(logsDir, 'phone_registrations.log');
    let logs = [];

    if (fs.existsSync(logFile)) {
      const logContent = fs.readFileSync(logFile, 'utf8');
      logs = logContent.trim().split('\n')
        .filter(line => line.trim())
        .map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return null;
          }
        })
        .filter(log => log !== null)
        .reverse(); // Mostrar los más recientes primero
    }

    res.send(`
      <html>
        <head>
          <title>Panel de Admin - Agente.ar</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f8f9fa; font-weight: bold; }
            .timestamp { font-family: monospace; font-size: 12px; }
            .phone { font-weight: bold; color: #007bff; }
            .ip { font-family: monospace; font-size: 12px; color: #666; }
            .back-link { display: inline-block; margin-bottom: 20px; color: #007bff; text-decoration: none; }
            .back-link:hover { text-decoration: underline; }
            .stats { background-color: #e9ecef; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <a href="/" class="back-link">← Volver al inicio</a>
            <h1>Panel de Administración</h1>

            <div class="stats">
              <strong>Estadísticas:</strong><br>
              Total de registros: ${logs.length}<br>
              Último registro: ${logs.length > 0 ? new Date(logs[0].timestamp).toLocaleString() : 'Ninguno'}
            </div>

            <h2>Registros de Teléfonos</h2>
            ${logs.length === 0 ? '<p>No hay registros aún.</p>' : `
              <table>
                <thead>
                  <tr>
                    <th>Timestamp</th>
                    <th>Teléfono</th>
                    <th>Acción</th>
                    <th>IP</th>
                  </tr>
                </thead>
                <tbody>
                  ${logs.map(log => `
                    <tr>
                      <td class="timestamp">${new Date(log.timestamp).toLocaleString()}</td>
                      <td class="phone">${log.phoneNumber}</td>
                      <td>${log.action}</td>
                      <td class="ip">${log.ip}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            `}
          </div>
        </body>
      </html>
    `);
  } catch (error) {
    console.error('Error al cargar admin:', error);
    res.status(500).send('<h1>Error</h1><p>Error al cargar el panel de administración</p>');
  }
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`Servidor corriendo en http://localhost:${port}`);
  console.log(`Accesible desde: https://app.agente.ar`);
});

// Manejo de errores
process.on('uncaughtException', (error) => {
  console.error('Error no capturado:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Promesa rechazada no manejada:', reason);
  process.exit(1);
});