const express = require('express');
const app = express();
const port = 3000;

// Middleware para parsear JSON
app.use(express.json());

// Ruta principal
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>App Agente.ar</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; }
          .container { max-width: 600px; margin: 0 auto; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>¡Hola Mundo desde Node.js!</h1>
          <p>Aplicación funcionando desde git</p>
          <p>Puerto: ${port}</p>
          <p>Timestamp: ${new Date().toLocaleString()}</p>
        </div>
      </body>
    </html>
  `);
});

// Ruta de prueba para auth (futura)
app.get('/auth', (req, res) => {
  res.send('<h1>Sección de Autenticación</h1><p>Próximamente: OAuth con Google Calendar</p>');
});

// Ruta de prueba para admin (futura)
app.get('/admin', (req, res) => {
  res.send('<h1>Panel de Admin</h1><p>Configuración del sistema</p>');
});

// Iniciar servidor
app.listen(port, () => {
  console.log(`Servidor corriendo en http://localhost:${port}`);
  console.log(`Accesible desde: https://app.agente.ar`);
});

// Manejo de errores
process.on('uncaughtException', (error) => {
  console.error('Error no capturado:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Promesa rechazada no manejada:', reason);
  process.exit(1);
});